// Set the basic API address for communication with the backend server
const baseUrl = "https://webrtc-backend-985795046666.us-central1.run.app";
// Flag indicating whether WebRTC is active, controls the enabling and disabling of connections
let isWebRTCActive = false;
// Create variables related to the WebRTC connection
let peerConnection;
let dataChannel;
let localAudioStream = null;  // store local mic stream globally
// Define an object that contains multiple functions; methods in fns will be called
const fns = {
    // Get the HTML content of the current page
    getPageHTML: () => {
        return {
            success: true,
            html: document.documentElement.outerHTML
        }; // Return the entire page's HTML
    },
    // Change the background color of the webpage
    changeBackgroundColor: ({ color }) => {
        document.body.style.backgroundColor = color; // Change the page's background color
        return { success: true, color }; // Return the changed color
    },
    // Change the text color of the webpage
    changeTextColor: ({ color }) => {
        document.body.style.color = color; // Change the page's text color
        return { success: true, color }; // Return the changed color
    },
    // Change the button's style (size and color)
    changeButtonStyle: ({ size, color }) => {
        const button = document.querySelector('button'); // Get the first button on the page (modify selector if there are multiple buttons)
        if (button) {
            // Change the button's size
            if (size) {
                button.style.fontSize = size; // Set font size
            }
            // Change the button's color
            if (color) {
                button.style.backgroundColor = color; // Set button background color
            }
            return { success: true, size, color }; // Return modified button style
        } else {
            return { success: false, message: 'Button element not found' }; // Return failure if no button is found
        }
    },

    // Toggle mute button functionality
    toggleMute: () => {
        const muteButton = document.getElementById('muteButton');
        if (muteButton) {
            const currentText = muteButton.textContent;
            if (currentText === 'Mute') {
                muteButton.textContent = 'Unmute';
                muteButton.style.backgroundColor = '#ff4444';
                muteButton.style.color = 'white';
                return { success: true, action: 'muted', buttonText: 'Unmute' };
            } else {
                muteButton.textContent = 'Mute';
                muteButton.style.backgroundColor = '';
                muteButton.style.color = '';
                return { success: true, action: 'unmuted', buttonText: 'Mute' };
            }
        }
        return { success: false, message: 'Mute button not found' };
    },

    // Change website title dynamically
    changeWebsiteTitle: ({ title }) => {
        document.title = title;
        const h1Element = document.querySelector('h1');
        if (h1Element) {
            h1Element.textContent = title;
        }
        return { success: true, title, message: `Website title changed to: ${title}` };
    },

    // Add dynamic notification banner
    addNotificationBanner: ({ message, type = 'info', duration = 5000 }) => {
        // Remove existing banner if present
        const existingBanner = document.getElementById('dynamicBanner');
        if (existingBanner) {
            existingBanner.remove();
        }

        const banner = document.createElement('div');
        banner.id = 'dynamicBanner';
        banner.textContent = message;

        // Style based on type
        const styles = {
            info: { backgroundColor: '#2196F3', color: 'white' },
            success: { backgroundColor: '#4CAF50', color: 'white' },
            warning: { backgroundColor: '#FF9800', color: 'white' },
            error: { backgroundColor: '#f44336', color: 'white' }
        };

        const style = styles[type] || styles.info;
        Object.assign(banner.style, {
            position: 'fixed',
            top: '0',
            left: '0',
            right: '0',
            padding: '15px',
            textAlign: 'center',
            fontSize: '16px',
            fontWeight: 'bold',
            zIndex: '9999',
            ...style
        });

        document.body.insertBefore(banner, document.body.firstChild);

        // Auto-remove after duration
        if (duration > 0) {
            setTimeout(() => {
                if (banner.parentNode) {
                    banner.remove();
                }
            }, duration);
        }

        return { success: true, message: `${type} banner added: ${message}`, duration };
    },

    // Create floating elements
    createFloatingElement: ({ text, color = '#FF6B6B', size = '20px' }) => {
        const floatingElement = document.createElement('div');
        floatingElement.textContent = text;
        floatingElement.className = 'floating-element';

        Object.assign(floatingElement.style, {
            position: 'fixed',
            top: Math.random() * 50 + 10 + '%',
            left: Math.random() * 80 + 10 + '%',
            backgroundColor: color,
            color: 'white',
            padding: '10px 15px',
            borderRadius: '25px',
            fontSize: size,
            fontWeight: 'bold',
            zIndex: '1000',
            cursor: 'pointer',
            boxShadow: '0 4px 8px rgba(0,0,0,0.3)',
            animation: 'float 3s ease-in-out infinite',
            userSelect: 'none'
        });

        // Add floating animation
        if (!document.getElementById('floatingStyles')) {
            const style = document.createElement('style');
            style.id = 'floatingStyles';
            style.textContent = `
                @keyframes float {
                    0%, 100% { transform: translateY(0px); }
                    50% { transform: translateY(-20px); }
                }
            `;
            document.head.appendChild(style);
        }

        // Remove on click
        floatingElement.addEventListener('click', () => {
            floatingElement.remove();
        });

        document.body.appendChild(floatingElement);

        return { success: true, text, color, size, message: `Floating element created: ${text}` };
    },

    // Change theme/mode
    changeTheme: ({ theme }) => {
        const themes = {
            dark: {
                backgroundColor: '#1a1a1a',
                color: '#ffffff',
                headerBg: '#333333',
                buttonBg: '#555555'
            },
            light: {
                backgroundColor: '#ffffff',
                color: '#000000',
                headerBg: '#f0f0f0',
                buttonBg: '#e0e0e0'
            },
            neon: {
                backgroundColor: '#000000',
                color: '#00ff00',
                headerBg: '#001100',
                buttonBg: '#003300'
            },
            ocean: {
                backgroundColor: '#001f3f',
                color: '#7FDBFF',
                headerBg: '#0074D9',
                buttonBg: '#39CCCC'
            }
        };

        const selectedTheme = themes[theme];
        if (!selectedTheme) {
            return { success: false, message: `Theme '${theme}' not found. Available themes: ${Object.keys(themes).join(', ')}` };
        }

        // Apply theme
        document.body.style.backgroundColor = selectedTheme.backgroundColor;
        document.body.style.color = selectedTheme.color;

        const header = document.querySelector('header');
        if (header) {
            header.style.backgroundColor = selectedTheme.headerBg;
        }

        const buttons = document.querySelectorAll('button');
        buttons.forEach(button => {
            button.style.backgroundColor = selectedTheme.buttonBg;
            button.style.color = selectedTheme.color;
            button.style.border = `2px solid ${selectedTheme.color}`;
        });

        return { success: true, theme, message: `Theme changed to: ${theme}` };
    },

    get_order_status: ({tracking_number}) => {
        const orders = {
            '263332508': {
                status: 'Delivered',
                message: 'Your order was delivered on May 25, 2025',
                estimated_delivery: '2025-05-25',
                location: 'Front door',
                carrier: 'FedEx',
                items: 'Electronics package'
            },
            '503504155': {
                status: 'In Transit',
                message: 'Your order is on the way and arriving soon!',
                estimated_delivery: '2025-05-28',
                location: 'Distribution center - Chicago',
                carrier: 'UPS',
                items: 'Home & Garden items'
            },
            '556657303': {
                status: 'Out for Delivery',
                message: 'Your order is out for delivery and will arrive today!',
                estimated_delivery: '2025-05-27',
                location: 'Local delivery truck',
                carrier: 'USPS',
                items: 'Books and media'
            },
            '708757140': {
                status: 'Processing',
                message: 'Your order is being prepared for shipment',
                estimated_delivery: '2025-05-30',
                location: 'Fulfillment center',
                carrier: 'DHL',
                items: 'Clothing and accessories'
            },
            '1068968393': {
                status: 'Shipped',
                message: 'Your order has been shipped and is on its way',
                estimated_delivery: '2025-05-29',
                location: 'In transit to destination',
                carrier: 'FedEx',
                items: 'Sports equipment'
            },
            '1068969375': {
                status: 'In Transit',
                message: 'Your order is moving through our network',
                estimated_delivery: '2025-05-31',
                location: 'Distribution center - Los Angeles',
                carrier: 'UPS',
                items: 'Kitchen appliances'
            },
            '1068980283': {
                status: 'Delivered',
                message: 'Your order was successfully delivered',
                estimated_delivery: '2025-05-26',
                location: 'Mailbox',
                carrier: 'USPS',
                items: 'Personal care products'
            },
            '1069028444': {
                status: 'Exception',
                message: 'Delivery attempted but recipient not available',
                estimated_delivery: '2025-05-28',
                location: 'Local post office - pickup available',
                carrier: 'USPS',
                items: 'Office supplies'
            },
            '1069609083': {
                status: 'In Transit',
                message: 'Your order is en route to the destination',
                estimated_delivery: '2025-06-01',
                location: 'Distribution center - Dallas',
                carrier: 'FedEx',
                items: 'Automotive parts'
            },
            '1069640200': {
                status: 'Out for Delivery',
                message: 'Your order is on the delivery truck',
                estimated_delivery: '2025-05-27',
                location: 'Local delivery vehicle',
                carrier: 'DHL',
                items: 'Pet supplies'
            },
            '**********': {
                status: 'Processing',
                message: 'Your order is being packed and will ship soon',
                estimated_delivery: '2025-06-02',
                location: 'Warehouse',
                carrier: 'UPS',
                items: 'Health and beauty'
            },
            '**********': {
                status: 'Shipped',
                message: 'Your order has left our facility',
                estimated_delivery: '2025-05-30',
                location: 'In transit',
                carrier: 'FedEx',
                items: 'Toys and games'
            },
            '**********': {
                status: 'In Transit',
                message: 'Your package is moving through our delivery network',
                estimated_delivery: '2025-06-03',
                location: 'Distribution center - Atlanta',
                carrier: 'UPS',
                items: 'Computer accessories'
            },
            '**********': {
                status: 'Delivered',
                message: 'Package delivered successfully',
                estimated_delivery: '2025-05-26',
                location: 'Back porch',
                carrier: 'FedEx',
                items: 'Garden tools'
            },
            '1069772661': {
                status: 'Exception',
                message: 'Address correction required',
                estimated_delivery: 'Pending',
                location: 'Sorting facility',
                carrier: 'USPS',
                items: 'Craft supplies'
            },
            '1069777802': {
                status: 'In Transit',
                message: 'Your order is on its way',
                estimated_delivery: '2025-06-01',
                location: 'Distribution center - Phoenix',
                carrier: 'DHL',
                items: 'Musical instruments'
            },
            '1069787810': {
                status: 'Out for Delivery',
                message: 'Your package will be delivered today',
                estimated_delivery: '2025-05-27',
                location: 'Delivery truck',
                carrier: 'UPS',
                items: 'Jewelry and watches'
            },
            '1069808126': {
                status: 'Processing',
                message: 'Order confirmed and being prepared',
                estimated_delivery: '2025-06-04',
                location: 'Fulfillment center',
                carrier: 'FedEx',
                items: 'Baby products'
            },
            '1069830758': {
                status: 'Shipped',
                message: 'Your order is on its way to you',
                estimated_delivery: '2025-06-02',
                location: 'In transit',
                carrier: 'USPS',
                items: 'Outdoor gear'
            },
            '1069875542': {
                status: 'In Transit',
                message: 'Package is moving through delivery network',
                estimated_delivery: '2025-06-05',
                location: 'Distribution center - Seattle',
                carrier: 'DHL',
                items: 'Art supplies'
            },
            '1069887025': {
                status: 'Delivered',
                message: 'Your order has been delivered',
                estimated_delivery: '2025-05-26',
                location: 'Front door',
                carrier: 'UPS',
                items: 'Food and beverages'
            },
            '1163059715': {
                status: 'Processing',
                message: 'Your order is being processed',
                estimated_delivery: '2025-06-06',
                location: 'Warehouse',
                carrier: 'FedEx',
                items: 'Furniture'
            },
            '1163343878': {
                status: 'Shipped',
                message: 'Order has been shipped',
                estimated_delivery: '2025-06-03',
                location: 'In transit to destination',
                carrier: 'USPS',
                items: 'Lighting fixtures'
            },
            '1163344156': {
                status: 'In Transit',
                message: 'Your package is en route',
                estimated_delivery: '2025-06-07',
                location: 'Distribution center - Denver',
                carrier: 'UPS',
                items: 'Power tools'
            },
            '1163844382': {
                status: 'Out for Delivery',
                message: 'Package is out for delivery',
                estimated_delivery: '2025-05-27',
                location: 'Local delivery truck',
                carrier: 'DHL',
                items: 'Smartphone accessories'
            },
            '1163883857': {
                status: 'Exception',
                message: 'Delivery delayed due to weather conditions',
                estimated_delivery: '2025-05-29',
                location: 'Local facility',
                carrier: 'FedEx',
                items: 'Winter clothing'
            }
        };

        if (orders[tracking_number]) {
            return orders[tracking_number];
        }

        return {
            status: 'Not Found',
            message: 'Tracking number not found in our system. Please check the number and try again.',
            estimated_delivery: 'Unknown',
            location: 'Unknown',
            carrier: 'Unknown',
            items: 'Unknown'
        };
    },

    // Create animated particles effect
    createParticleEffect: ({ count = 50, color = '#FFD700' }) => {
        // Remove existing particles
        document.querySelectorAll('.particle').forEach(p => p.remove());

        for (let i = 0; i < count; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';

            Object.assign(particle.style, {
                position: 'fixed',
                width: '4px',
                height: '4px',
                backgroundColor: color,
                borderRadius: '50%',
                left: Math.random() * window.innerWidth + 'px',
                top: Math.random() * window.innerHeight + 'px',
                zIndex: '999',
                pointerEvents: 'none',
                animation: `particle-float ${2 + Math.random() * 3}s ease-in-out infinite`
            });

            document.body.appendChild(particle);
        }

        // Add particle animation styles
        if (!document.getElementById('particleStyles')) {
            const style = document.createElement('style');
            style.id = 'particleStyles';
            style.textContent = `
                @keyframes particle-float {
                    0%, 100% {
                        transform: translateY(0px) rotate(0deg);
                        opacity: 1;
                    }
                    50% {
                        transform: translateY(-30px) rotate(180deg);
                        opacity: 0.7;
                    }
                }
            `;
            document.head.appendChild(style);
        }

        // Auto-remove particles after 10 seconds
        setTimeout(() => {
            document.querySelectorAll('.particle').forEach(p => p.remove());
        }, 10000);

        return { success: true, count, color, message: `Created ${count} particles with color ${color}` };
    },

    // Add countdown timer
    addCountdownTimer: ({ seconds, message = 'Timer' }) => {
        // Remove existing timer
        const existingTimer = document.getElementById('countdownTimer');
        if (existingTimer) {
            existingTimer.remove();
        }

        const timer = document.createElement('div');
        timer.id = 'countdownTimer';

        Object.assign(timer.style, {
            position: 'fixed',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            color: '#00ff00',
            padding: '20px 40px',
            borderRadius: '10px',
            fontSize: '24px',
            fontWeight: 'bold',
            zIndex: '10000',
            textAlign: 'center',
            border: '2px solid #00ff00',
            fontFamily: 'monospace'
        });

        let timeLeft = seconds;

        const updateTimer = () => {
            timer.innerHTML = `${message}<br><span style="font-size: 36px; color: #ff6b6b;">${timeLeft}</span>`;

            if (timeLeft <= 0) {
                timer.innerHTML = `${message}<br><span style="font-size: 36px; color: #4CAF50;">DONE!</span>`;
                setTimeout(() => timer.remove(), 2000);
                return;
            }

            timeLeft--;
            setTimeout(updateTimer, 1000);
        };

        document.body.appendChild(timer);
        updateTimer();

        return { success: true, seconds, message: `Countdown timer started: ${seconds} seconds` };
    },

    // Create matrix rain effect
    createMatrixEffect: ({ duration = 10000 }) => {
        // Remove existing matrix
        const existingMatrix = document.getElementById('matrixCanvas');
        if (existingMatrix) {
            existingMatrix.remove();
        }

        const canvas = document.createElement('canvas');
        canvas.id = 'matrixCanvas';
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;

        Object.assign(canvas.style, {
            position: 'fixed',
            top: '0',
            left: '0',
            zIndex: '999',
            pointerEvents: 'none',
            backgroundColor: 'rgba(0, 0, 0, 0.1)'
        });

        document.body.appendChild(canvas);

        const ctx = canvas.getContext('2d');
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789@#$%^&*()';
        const fontSize = 14;
        const columns = canvas.width / fontSize;
        const drops = Array(Math.floor(columns)).fill(1);

        const draw = () => {
            ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            ctx.fillStyle = '#0F0';
            ctx.font = fontSize + 'px monospace';

            for (let i = 0; i < drops.length; i++) {
                const text = chars[Math.floor(Math.random() * chars.length)];
                ctx.fillText(text, i * fontSize, drops[i] * fontSize);

                if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {
                    drops[i] = 0;
                }
                drops[i]++;
            }
        };

        const interval = setInterval(draw, 33);

        // Remove after duration
        setTimeout(() => {
            clearInterval(interval);
            canvas.remove();
        }, duration);

        return { success: true, duration, message: `Matrix effect started for ${duration}ms` };
    },

    // Add interactive widget
    addWidget: ({ type, content = 'Widget Content' }) => {
        const widgets = {
            clock: () => {
                const widget = document.createElement('div');
                widget.className = 'widget clock-widget';

                Object.assign(widget.style, {
                    position: 'fixed',
                    top: '20px',
                    right: '20px',
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    color: '#00ff00',
                    padding: '15px',
                    borderRadius: '10px',
                    fontSize: '18px',
                    fontFamily: 'monospace',
                    zIndex: '1000',
                    border: '2px solid #00ff00'
                });

                const updateClock = () => {
                    const now = new Date();
                    widget.textContent = now.toLocaleTimeString();
                };

                updateClock();
                const clockInterval = setInterval(updateClock, 1000);

                // Store interval for cleanup
                widget.clockInterval = clockInterval;

                return widget;
            },

            weather: () => {
                const widget = document.createElement('div');
                widget.className = 'widget weather-widget';

                Object.assign(widget.style, {
                    position: 'fixed',
                    top: '20px',
                    left: '20px',
                    backgroundColor: 'rgba(30, 144, 255, 0.9)',
                    color: 'white',
                    padding: '15px',
                    borderRadius: '10px',
                    fontSize: '16px',
                    zIndex: '1000',
                    minWidth: '150px'
                });

                widget.innerHTML = `
                    <div style="font-weight: bold;">🌤️ Weather</div>
                    <div>72°F / 22°C</div>
                    <div>Partly Cloudy</div>
                    <div style="font-size: 12px; margin-top: 5px;">San Francisco, CA</div>
                `;

                return widget;
            },

            note: () => {
                const widget = document.createElement('div');
                widget.className = 'widget note-widget';

                Object.assign(widget.style, {
                    position: 'fixed',
                    bottom: '20px',
                    right: '20px',
                    backgroundColor: '#ffeb3b',
                    color: '#333',
                    padding: '15px',
                    borderRadius: '10px',
                    fontSize: '14px',
                    zIndex: '1000',
                    maxWidth: '200px',
                    boxShadow: '0 4px 8px rgba(0,0,0,0.3)'
                });

                widget.innerHTML = `
                    <div style="font-weight: bold; margin-bottom: 5px;">📝 Note</div>
                    <div>${content}</div>
                `;

                return widget;
            }
        };

        // Remove existing widget of same type
        const existingWidget = document.querySelector(`.${type}-widget`);
        if (existingWidget) {
            if (existingWidget.clockInterval) {
                clearInterval(existingWidget.clockInterval);
            }
            existingWidget.remove();
        }

        const widgetCreator = widgets[type];
        if (!widgetCreator) {
            return { success: false, message: `Widget type '${type}' not found. Available: ${Object.keys(widgets).join(', ')}` };
        }

        const widget = widgetCreator();

        // Add close button
        const closeBtn = document.createElement('span');
        closeBtn.innerHTML = '×';
        Object.assign(closeBtn.style, {
            position: 'absolute',
            top: '5px',
            right: '8px',
            cursor: 'pointer',
            fontSize: '16px',
            fontWeight: 'bold'
        });

        closeBtn.addEventListener('click', () => {
            if (widget.clockInterval) {
                clearInterval(widget.clockInterval);
            }
            widget.remove();
        });

        widget.style.position = 'relative';
        widget.appendChild(closeBtn);
        document.body.appendChild(widget);

        return { success: true, type, content, message: `${type} widget added successfully` };
    }
};

// When an audio stream is received, add it to the page and play it
function handleTrack(event) {
    const el = document.createElement('audio'); // Create an audio element
    el.srcObject = event.streams[0]; // Set the audio stream as the element's source
    el.autoplay = el.controls = true; // Autoplay and display audio controls
    document.body.appendChild(el); // Add the audio element to the page
}

// Create a data channel for transmitting control messages (such as function calls)
function createDataChannel() {
    // Create a data channel named 'response'
    dataChannel = peerConnection.createDataChannel('response');
    // Configure data channel events
    dataChannel.addEventListener('open', async () => {
        console.log('Data channel opened - ready to configure functions');
        console.log('Data channel state:', dataChannel.readyState);

        // Get organisation_id from URL parameters or localStorage
        const urlParams = new URLSearchParams(window.location.search);
        const organisationId = urlParams.get('organisation_id') || localStorage.getItem('organisation_id');

        await configureData(organisationId); // Configure data channel functions
    });
    dataChannel.addEventListener('message', async (ev) => {
        const msg = JSON.parse(ev.data); // Parse the received message
        // If the message type is 'response.function_call_arguments.done', it indicates a function call request
        if (msg.type === 'response.function_call_arguments.done') {
            const fn = fns[msg.name]; // Get the corresponding function by name
            if (fn !== undefined) {
                console.log(`Calling local function ${msg.name}, parameters ${msg.arguments}`);

                try {
                    const args = JSON.parse(msg.arguments); // Parse function parameters
                    const result = await fn(args); // Call the local function and wait for the result
                    console.log('Function result:', result); // Log the result of the function

                    // Send the result of the function execution back to the other party
                    const event = {
                        type: 'conversation.item.create', // Create conversation item event
                        item: {
                            type: 'function_call_output', // Function call output
                            call_id: msg.call_id, // Passed call_id
                            output: JSON.stringify(result), // JSON string of the function execution result
                        },
                    };

                    // Send function result
                    dataChannel.send(JSON.stringify(event));

                    // Small delay to ensure the function output is processed before requesting response
                    setTimeout(() => {
                        const response = {"type": "response.create"};
                        dataChannel.send(JSON.stringify(response));
                        console.log('Requested model response after function execution');
                    }, 100);

                } catch (error) {
                    console.error('Function execution error:', error);
                    // Send error as function output
                    const errorEvent = {
                        type: 'conversation.item.create',
                        item: {
                            type: 'function_call_output',
                            call_id: msg.call_id,
                            output: JSON.stringify({error: error.message}),
                        },
                    };
                    dataChannel.send(JSON.stringify(errorEvent));

                    // Still trigger response even on error
                    setTimeout(() => {
                        const response = {"type": "response.create"};
                        dataChannel.send(JSON.stringify(response));
                    }, 100);
                }
            } else {
                console.error(`Function ${msg.name} not found`);
                // Send error for unknown function
                const errorEvent = {
                    type: 'conversation.item.create',
                    item: {
                        type: 'function_call_output',
                        call_id: msg.call_id,
                        output: JSON.stringify({error: `Function ${msg.name} not found`}),
                    },
                };
                dataChannel.send(JSON.stringify(errorEvent));

                setTimeout(() => {
                    const response = {"type": "response.create"};
                    dataChannel.send(JSON.stringify(response));
                }, 100);
            }
        }
    });
}

// Configure data channel functions and tools
async function configureData(organisationId = null) {
    console.log('Configuring data channel');

    let sessionConfig = {
        modalities: ['text', 'audio'],
        tools: [],
        instructions: 'You are a helpful assistant.'
    };

    // If organisation_id is provided, fetch configuration from database
    if (organisationId) {
        try {
            console.log(`Fetching session config for organisation: ${organisationId}`);
            const response = await fetch(`${baseUrl}/api/session-config/${organisationId}`);
            if (response.ok) {
                const dbConfig = await response.json();
                sessionConfig.tools = dbConfig.tools || [];
                sessionConfig.instructions = dbConfig.instructions || sessionConfig.instructions;
                console.log('Loaded session config from database');
            } else {
                console.warn('Failed to fetch session config from database, using default tools');
                sessionConfig.tools = getDefaultTools();
            }
        } catch (error) {
            console.error('Error fetching session config:', error);
            sessionConfig.tools = getDefaultTools();
        }
    } else {
        // Use default hardcoded tools if no organisation_id provided
        console.log('No ID provided, setting default tools...')
        sessionConfig.tools = getDefaultTools();
    }

    const event = {
        type: 'session.update',
        session: sessionConfig
    };

    // Debug logging
    console.log('Sending session.update with tools:', event.session.tools.length, 'tools');
    console.log('Tool names:', event.session.tools.map(t => t.name));

    dataChannel.send(JSON.stringify(event));
    console.log('Session update sent successfully');
}

// Default tools function (extracted from original hardcoded tools)
function getDefaultTools() {
    return [
                {
                    type: 'function', // Tool type is function
                    name: 'changeBackgroundColor', // Function name
                    description: 'Change the background color of the webpage', // Description
                    parameters: { // Parameter description
                        type: 'object',
                        properties: {
                            color: {
                                type: 'string',
                                description: 'Hexadecimal value of the color'
                            }, // Color parameter
                        },
                    },
                },
                {
                    type: 'function',
                    name: 'changeTextColor',
                    description: 'Change the text color of the webpage',
                    parameters: {
                        type: 'object',
                        properties: {
                            color: {
                                type: 'string',
                                description: 'Hexadecimal value of the color'
                            },
                        },
                    },
                },
                {
                    type: 'function',
                    name: 'getPageHTML',
                    description: 'Get the HTML content of the current page',
                },
                {
                    type: 'function', // Tool type is function
                    name: 'changeButtonStyle', // New function name
                    description: 'Change the size and color of the button', // Description
                    parameters: { // Parameter description
                        type: 'object',
                        properties: {
                            size: {
                                type: 'string',
                                description: 'Font size of the button (e.g., "16px" or "1em")'
                            }, // Button size
                            color: {
                                type: 'string',
                                description: 'Background color of the button (e.g., "#ff0000" or "red")'
                            }, // Button color
                        },
                    },
                },
                {
                    type: 'function', // Tool type is function
                    name: 'get_order_status', // New function name
                    description: 'Fetch and report the status of an order. Ask the user for their tracking number, which should be a 9 or 10 digit numeric string. Tell the user this can be provided verbally. DO NOT call this function until the tracking number is confirmed and fully captured.  After receiving the details, inform the user about the status of their order and ask if they would like additional information such as the recipient or arrival date. If the user disputes the order status, offer to register a complaint.', // Description
                    parameters: { // Parameter description
                        type: 'object',
                        properties: {
                            tracking_number: {
                                type: 'string',
                                description: "The consignment number for the order, as a 9 or 10 digit numeric string (e.g., '263332508'). Provided via voice."
                            }, // order_status
                        },
                    },
                },
                {
                    type: 'function',
                    name: 'toggleMute',
                    description: 'Toggle the mute button state and appearance. Changes button text between Mute/Unmute and updates styling.',
                    parameters: {
                        type: 'object',
                        properties: {}
                    }
                },
                {
                    type: 'function',
                    name: 'changeWebsiteTitle',
                    description: 'Change the website title and main heading dynamically. This will update both the browser tab title and the main page heading.',
                    parameters: {
                        type: 'object',
                        properties: {
                            title: {
                                type: 'string',
                                description: 'The new title for the website'
                            }
                        },
                        required: ['title']
                    }
                },
                {
                    type: 'function',
                    name: 'addNotificationBanner',
                    description: 'Add a notification banner at the top of the page with customizable message, type, and duration.',
                    parameters: {
                        type: 'object',
                        properties: {
                            message: {
                                type: 'string',
                                description: 'The message to display in the banner'
                            },
                            type: {
                                type: 'string',
                                enum: ['info', 'success', 'warning', 'error'],
                                description: 'The type of notification (affects color scheme)'
                            },
                            duration: {
                                type: 'number',
                                description: 'Duration in milliseconds before auto-removal (0 for permanent)'
                            }
                        },
                        required: ['message']
                    }
                },
                {
                    type: 'function',
                    name: 'createFloatingElement',
                    description: 'Create floating animated elements on the page with custom text, color, and size.',
                    parameters: {
                        type: 'object',
                        properties: {
                            text: {
                                type: 'string',
                                description: 'Text to display in the floating element'
                            },
                            color: {
                                type: 'string',
                                description: 'Background color of the floating element (hex or color name)'
                            },
                            size: {
                                type: 'string',
                                description: 'Font size of the text (e.g., "20px", "1.5em")'
                            }
                        },
                        required: ['text']
                    }
                },
                {
                    type: 'function',
                    name: 'changeTheme',
                    description: 'Change the overall theme/appearance of the website. Available themes: dark, light, neon, ocean.',
                    parameters: {
                        type: 'object',
                        properties: {
                            theme: {
                                type: 'string',
                                enum: ['dark', 'light', 'neon', 'ocean'],
                                description: 'The theme to apply to the website'
                            }
                        },
                        required: ['theme']
                    }
                },
                {
                    type: 'function',
                    name: 'createParticleEffect',
                    description: 'Create animated floating particles across the screen for a magical effect.',
                    parameters: {
                        type: 'object',
                        properties: {
                            count: {
                                type: 'number',
                                description: 'Number of particles to create (default: 50)'
                            },
                            color: {
                                type: 'string',
                                description: 'Color of the particles (hex or color name, default: #FFD700)'
                            }
                        }
                    }
                },
                {
                    type: 'function',
                    name: 'addCountdownTimer',
                    description: 'Add a prominent countdown timer overlay to the center of the screen.',
                    parameters: {
                        type: 'object',
                        properties: {
                            seconds: {
                                type: 'number',
                                description: 'Number of seconds to count down from'
                            },
                            message: {
                                type: 'string',
                                description: 'Message to display above the countdown (default: "Timer")'
                            }
                        },
                        required: ['seconds']
                    }
                },
                {
                    type: 'function',
                    name: 'createMatrixEffect',
                    description: 'Create a Matrix-style digital rain effect overlay on the screen.',
                    parameters: {
                        type: 'object',
                        properties: {
                            duration: {
                                type: 'number',
                                description: 'Duration of the effect in milliseconds (default: 10000)'
                            }
                        }
                    }
                },
                {
                    type: 'function',
                    name: 'addWidget',
                    description: 'Add interactive widgets to the page. Available types: clock (live time), weather (mock weather info), note (custom note).',
                    parameters: {
                        type: 'object',
                        properties: {
                            type: {
                                type: 'string',
                                enum: ['clock', 'weather', 'note'],
                                description: 'Type of widget to add'
                            },
                            content: {
                                type: 'string',
                                description: 'Content for the widget (used for note type)'
                            }
                        },
                        required: ['type']
                    }
                },
            ];
}

// Get the control button element
const toggleButton = document.getElementById('toggleWebRTCButton');
// Add a click event listener to the button to toggle the WebRTC connection state
toggleButton.addEventListener('click', () => {
    // If WebRTC is active, stop the connection; otherwise, start WebRTC
    if (isWebRTCActive) {
        stopWebRTC(); // Stop WebRTC
        toggleButton.textContent = 'start'; // Update button text
    } else {
        startWebRTC(); // Start WebRTC
        toggleButton.textContent = 'stop'; // Update button text
    }
});

const muteButton = document.getElementById('muteButton');
muteButton.addEventListener('click', toggleMute);


let isMuted = false;

function toggleMute() {
    if (!localAudioStream) return;

    localAudioStream.getAudioTracks().forEach(track => {
        track.enabled = !track.enabled;  // toggle enabled
    });

    isMuted = !isMuted;
    const muteButton = document.getElementById('muteButton');
    muteButton.textContent = isMuted ? 'Unmute' : 'Mute';
    console.log(isMuted ? 'Audio muted' : 'Audio unmuted');
}


// Capture microphone input stream and initiate WebRTC connection
function startWebRTC() {
    // If WebRTC is already active, return directly
    if (isWebRTCActive) return;
    // Create a new peerConnection object to establish a WebRTC connection
    peerConnection = new RTCPeerConnection();
    peerConnection.ontrack = handleTrack; // Bind audio stream processing function
    createDataChannel(); // Create data channel
    console.log('Starting WebRTC');
    // Request user's audio stream
    navigator.mediaDevices.getUserMedia({ audio: true }).then((stream) => {
        console.log('Got audio stream');
        localAudioStream = stream;
        // Add each track from the audio stream to the peerConnection
        stream.getTracks().forEach((track) => peerConnection.addTransceiver(track, { direction: 'sendrecv' }));
        // Create an offer for the local connection
        peerConnection.createOffer().then((offer) => {
            console.log('Created offer');
            localAudioStream = stream;
            peerConnection.setLocalDescription(offer); // Set local description (offer)
            // Send the offer to the backend for signaling exchange
            fetch(baseUrl + '/api/rtc-connect', {
                method: 'POST',
                body: offer.sdp, // Send the SDP of the offer to the backend
                headers: {
                    'Content-Type': 'application/sdp',
                },
            })
            .then((r) => r.text())
            .then((answer) => {
                console.log('Got answer');
                // Get the answer returned by the backend and set it as the remote description
                peerConnection.setRemoteDescription({ sdp: answer, type: 'answer' });
            });
        });
    });
    // Mark WebRTC as active
    isWebRTCActive = true;
}

// Stop the WebRTC connection and clean up all resources
function stopWebRTC() {
    // If WebRTC is not active, return directly
    if (!isWebRTCActive) return;
    console.log('Stopping WebRTC');
    // Stop the received audio tracks
    const tracks = peerConnection.getReceivers().map(receiver => receiver.track);
    tracks.forEach(track => track.stop());
    // Close the data channel and WebRTC connection
    if (dataChannel) dataChannel.close();
    if (peerConnection) peerConnection.close();
    // Reset connection and channel objects
    peerConnection = null;
    dataChannel = null;
    // Mark WebRTC as not active
    isWebRTCActive = false;
}
