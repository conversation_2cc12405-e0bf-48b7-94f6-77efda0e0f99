#!/usr/bin/env python3
"""
Session Configuration API for WebRTC Frontend
Provides endpoints to fetch session configuration from PostgreSQL database
"""

import os
import json
import asyncio
import asyncpg
import logging
from typing import Optional, Dict, List, Any
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database configuration
DB_CONFIG = {
    'user': os.getenv('user', 'chatbot_user'),
    'password': os.getenv('password', 'ChatBot@123'),
    'database': os.getenv('dbname', 'call_centre_analytics'),
    'host': os.getenv('host', 'najoomi.clw2kwwaavrk.us-east-1.rds.amazonaws.com'),
    'port': os.getenv('port', '5432'),
    'min_size': 1,
    'max_size': 10
}

# Global database pool
db_pool = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """FastAPI lifespan context manager for database connection pool management."""
    global db_pool
    logger.info("Application startup: Initializing database connection pool...")
    try:
        db_pool = await asyncpg.create_pool(**DB_CONFIG)
        logger.info("Database connection pool created successfully")
        yield
    except Exception as e:
        logger.error(f"Failed to create database pool: {e}")
        raise
    finally:
        logger.info("Application shutdown: Closing database connection pool...")
        if db_pool:
            await db_pool.close()

# FastAPI app instance
app = FastAPI(
    title="Session Configuration API",
    description="API for fetching WebRTC session configuration from database",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class SessionConfig(BaseModel):
    tools: List[Dict[str, Any]]
    instructions: str
    modalities: List[str] = ["text", "audio"]

class FunctionTool(BaseModel):
    type: str = "function"
    name: str
    description: str
    parameters: Dict[str, Any]

# Dependency to get database pool
async def get_pool() -> asyncpg.Pool:
    """Get the database connection pool."""
    if db_pool is None:
        raise HTTPException(
            status_code=503,
            detail="Database service is currently unavailable"
        )
    return db_pool

async def fetch_session_config_from_db(pool: asyncpg.Pool, organisation_id: str) -> Optional[SessionConfig]:
    """Fetch session configuration from database for given organisation_id."""
    try:
        async with pool.acquire() as conn:
            # Query to get function definitions and system prompts for the organisation
            query = """
                SELECT function_name, function_schema, system_prompt, api_link, method_type
                FROM chatbot.function_definitions
                WHERE organisation_id = $1
            """
            results = await conn.fetch(query, organisation_id)
            
            if not results:
                logger.warning(f"No configuration found for organisation_id: {organisation_id}")
                return None
            
            # Process results to build session configuration
            tools = []
            instructions_list = []
            
            for result in results:
                # Parse function schema
                if isinstance(result['function_schema'], str):
                    function_tools = json.loads(result['function_schema'])
                else:
                    function_tools = result['function_schema']
                
                # Convert to the realtime API's required format
                for tool in function_tools:
                    function_data = tool.get("function", {})
                    tools.append({
                        "type": "function",
                        "name": function_data.get("name"),
                        "description": function_data.get("description"),
                        "parameters": function_data.get("parameters")
                    })
                
                # Collect system prompts
                if result['system_prompt']:
                    instructions_list.append(result['system_prompt'])
            
            # Combine all instructions
            combined_instructions = " ".join(instructions_list) if instructions_list else "You are a helpful assistant."
            
            return SessionConfig(
                tools=tools,
                instructions=combined_instructions,
                modalities=["text", "audio"]
            )
            
    except Exception as e:
        logger.error(f"Database error while fetching session config: {e}")
        raise HTTPException(status_code=500, detail="Database error")

@app.get("/api/session-config/{organisation_id}", response_model=SessionConfig)
async def get_session_config(
    organisation_id: str,
    pool: asyncpg.Pool = Depends(get_pool)
) -> SessionConfig:
    """
    Get session configuration for a specific organisation.
    
    Args:
        organisation_id: The organisation identifier
        
    Returns:
        SessionConfig: Configuration including tools and instructions
    """
    logger.info(f"Fetching session config for organisation: {organisation_id}")
    
    config = await fetch_session_config_from_db(pool, organisation_id)
    
    if config is None:
        raise HTTPException(
            status_code=404,
            detail=f"No configuration found for organisation_id: {organisation_id}"
        )
    
    logger.info(f"Successfully retrieved {len(config.tools)} tools for organisation: {organisation_id}")
    return config

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "service": "session-config-api"}

if __name__ == "__main__":
    import uvicorn
    port = int(os.getenv("PORT", 8001))
    uvicorn.run(app, host="0.0.0.0", port=port)
