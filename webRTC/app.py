#!/usr/bin/env python3
"""
Integrated WebRTC Backend with Session Configuration API
Combines WebRTC functionality with database-driven session configuration
"""

import os
import json
import asyncio
import asyncpg
import logging
import requests
from typing import Optional, Dict, List, Any
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import Response
from pydantic import BaseModel
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration - Load from environment variables
OPENAI_API_KEY = os.environ.get('OPENAI_API_KEY')
if not OPENAI_API_KEY:
    logger.error("OPENAI_API_KEY environment variable not set.")
    raise EnvironmentError("OPENAI_API_KEY environment variable not set.")

OPENAI_SESSION_URL = os.environ.get('OPENAI_SESSION_URL')
OPENAI_API_URL = os.environ.get('OPENAI_API_URL')
MODEL_ID = os.environ.get('MODEL')
VOICE = os.environ.get('VOICE')
DEFAULT_INSTRUCTIONS = os.environ.get('DEFAULT_INSTRUCTIONS', 'You are a helpful AI assistant and have some tools installed. Converse in the Urdu language, use simple vocabulary and speak clearly. Talk quickly, but remain friendly and stay on topic at all times.')
PORT = int(os.environ.get('PORT', 5055))

# Database configuration
DB_CONFIG = {
    'user': os.getenv('user', 'chatbot_user'),
    'password': os.getenv('password', 'ChatBot@123'),
    'database': os.getenv('dbname', 'call_centre_analytics'),
    'host': os.getenv('host', 'najoomi.clw2kwwaavrk.us-east-1.rds.amazonaws.com'),
    'port': os.getenv('port', '5432'),
    'min_size': 1,
    'max_size': 10
}

# Global database pool
db_pool = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """FastAPI lifespan context manager for database connection pool management."""
    global db_pool
    logger.info("Application startup: Initializing database connection pool...")
    try:
        db_pool = await asyncpg.create_pool(**DB_CONFIG)
        logger.info("Database connection pool created successfully")
        yield
    except Exception as e:
        logger.error(f"Failed to create database pool: {e}")
        raise
    finally:
        logger.info("Application shutdown: Closing database connection pool...")
        if db_pool:
            await db_pool.close()

# Log configuration (without sensitive data)
logger.info("Configuration loaded:")
logger.info(f"  OPENAI_SESSION_URL: {OPENAI_SESSION_URL}")
logger.info(f"  OPENAI_API_URL: {OPENAI_API_URL}")
logger.info(f"  MODEL: {MODEL_ID}")
logger.info(f"  VOICE: {VOICE}")
logger.info(f"  PORT: {PORT}")
logger.info(f"  OPENAI_API_KEY: {'*' * (len(OPENAI_API_KEY) - 8) + OPENAI_API_KEY[-8:] if OPENAI_API_KEY else 'Not set'}")
logger.info(f"  DEFAULT_INSTRUCTIONS: {DEFAULT_INSTRUCTIONS}")

# FastAPI app instance
app = FastAPI(
    title="Integrated WebRTC Backend",
    description="WebRTC backend with session configuration from database",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class SessionConfig(BaseModel):
    tools: List[Dict[str, Any]]
    instructions: str
    modalities: List[str] = ["text", "audio"]

class FunctionTool(BaseModel):
    type: str = "function"
    name: str
    description: str
    parameters: Dict[str, Any]

# Dependency to get database pool
async def get_pool() -> asyncpg.Pool:
    """Get the database connection pool."""
    if db_pool is None:
        raise HTTPException(
            status_code=503,
            detail="Database service is currently unavailable"
        )
    return db_pool

@app.get("/")
async def home():
    return {"message": "WebRTC Backend Running!", "status": "healthy"}

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "service": "integrated-webrtc-backend"}

@app.post("/api/rtc-connect")
async def connect_rtc(request: Request):
    """
    RTC connection endpoint for handling WebRTC SDP exchange and generating/using ephemeral tokens.
    """
    try:
        # Step 1: Retrieve the client's SDP from the request body
        client_sdp = (await request.body()).decode('utf-8')
        if not client_sdp:
            logger.error("No SDP provided in the request body.")
            return Response("No SDP provided in the request body.", status_code=400)

        logger.info("Received SDP from client.")

        # Step 2: Generate ephemeral API token
        token_headers = {
            "Authorization": f"Bearer {OPENAI_API_KEY}",
            "Content-Type": "application/json"
        }
        token_payload = {
            "model": MODEL_ID,
            "voice": VOICE
        }

        logger.info("Requesting ephemeral token from OpenAI.")

        token_response = requests.post(OPENAI_SESSION_URL, headers=token_headers, json=token_payload)

        if not token_response.ok:
            logger.error(f"Failed to obtain ephemeral token, status code: {token_response.status_code}, response: {token_response.text}")
            return Response(f"Failed to obtain ephemeral token, status code: {token_response.status_code}", status_code=500)

        token_data = token_response.json()
        # Adjust the path based on the actual response structure
        # Assuming the ephemeral token is located at `client_secret.value`
        ephemeral_token = token_data.get('client_secret', {}).get('value', '')

        if not ephemeral_token:
            logger.error("Ephemeral token is empty or not found in the response.")
            return Response("Ephemeral token is empty or not found in the response.", status_code=500)

        logger.info("Ephemeral token obtained successfully.")

        # Step 3: Perform SDP exchange with OpenAI's Realtime API using the ephemeral token
        sdp_headers = {
            "Authorization": f"Bearer {ephemeral_token}",
            "Content-Type": "application/sdp"
        }
        sdp_params = {
            "model": MODEL_ID,
            "instructions": DEFAULT_INSTRUCTIONS,
            "voice": VOICE
        }

        # Build the full URL with query parameters
        sdp_url = requests.Request('POST', OPENAI_API_URL, params=sdp_params).prepare().url

        logger.info(f"Sending SDP to OpenAI Realtime API at {sdp_url}")

        sdp_response = requests.post(sdp_url, headers=sdp_headers, data=client_sdp)

        if not sdp_response.ok:
            logger.error(f"OpenAI API SDP exchange error, status code: {sdp_response.status_code}, response: {sdp_response.text}")
            return Response(f"OpenAI API SDP exchange error, status code: {sdp_response.status_code}", status_code=500)

        logger.info("SDP exchange with OpenAI completed successfully.")

        # Step 4: Return OpenAI's SDP response to the client with the correct content type
        return Response(
            content=sdp_response.content,
            status_code=200,
            media_type='application/sdp'
        )

    except Exception as e:
        logger.exception("An error occurred during the RTC connection process.")
        return Response(f"An error occurred: {str(e)}", status_code=500)

async def fetch_session_config_from_db(pool: asyncpg.Pool, organisation_id: str) -> Optional[SessionConfig]:
    """Fetch session configuration from database for given organisation_id."""
    try:
        async with pool.acquire() as conn:
            # Query to get function definitions and system prompts for the organisation
            query = """
                SELECT function_name, function_schema, system_prompt, api_link, method_type
                FROM chatbot.function_definitions
                WHERE organisation_id = $1
            """
            result = await conn.fetch(query, organisation_id)

            await conn.close()
            if not result:
                return None, None, None
            logger.info('Data received')
            all_base_urls, all_function_names, all_methods = [], [], []
            function_tools = result[0][1]
            prompt = result[0][2]
            for row in result:
                api_link = row['api_link']
                function_name = row['function_name']
                method = row['method_type']
                all_base_urls.append(api_link)
                all_function_names.append(function_name)
                all_methods.append(method)

            function_tools = json.loads(function_tools)

            # Convert to the realtime api's required format
            converted_tools = []

            for tool in function_tools:
                function_data = tool.get("function", {})
                converted_tools.append({
                    "type": "function",
                    "name": function_data.get("name"),
                    "description": function_data.get("description"),
                    "parameters": function_data.get("parameters")
                })
                
            api_links = {i:[j,k] for i, j, k in zip(all_function_names, all_base_urls, all_methods)}

            logger.info('Data processed')

            return converted_tools, prompt, api_links

    except Exception as e:
        logger.error(f"Database error while fetching session config: {e}")
        raise HTTPException(status_code=500, detail="Database error")

@app.get("/api/session-config/{organisation_id}", response_model=SessionConfig)
async def get_session_config(
    organisation_id: str,
    pool: asyncpg.Pool = Depends(get_pool)
) -> SessionConfig:
    """
    Get session configuration for a specific organisation.

    Args:
        organisation_id: The organisation identifier

    Returns:
        SessionConfig: Configuration including tools and instructions
    """
    logger.info(f"Fetching session config for organisation: {organisation_id}")

    tools, prompt, api_links = await fetch_session_config_from_db(pool, organisation_id)

    if tools is None:
        raise HTTPException(
            status_code=404,
            detail=f"No configuration found for organisation_id: {organisation_id}"
        )

    logger.info(f"Successfully retrieved {len(tools)} tools for organisation: {organisation_id}")
    return tools, prompt, api_links



if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=PORT)