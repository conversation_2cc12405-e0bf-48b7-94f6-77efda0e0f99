// Import the built-in HTTP and File System modules
const http = require('http');
const fs = require('fs');

// Define the host and port the server will run on
const hostname = '127.0.0.1'; // This means localhost
const port = 3000;

// Create the server
const server = http.createServer((req, res) => {
  // This callback function runs every time a request hits the server
  console.log(`Request received at ${new Date().toISOString()}`);
  console.log(req.url);

  // Read the index.html file from the disk
  fs.readFile('index.html', (err, htmlContent) => {
    // If there's an error reading the file (e.g., file not found)
    if (err) {
      res.writeHead(500, { 'Content-Type': 'text/plain' });
      res.end('Error: Could not read HTML file.');
      return; // Stop execution
    } else {
      console.log('HTML file read successfully');
    }
    console.log('Sending response...');
    console.log('Response sent');
    console.log('----------------------------------------');
  
    try {
      res.writeHead(200, { 'Content-Type': 'text/html' });
      res.end(htmlContent);
    } catch (error) {
      console.error('Error sending response:', error);
    }
    // If the file is read successfully
    
  });
});

// Start the server and have it listen on the specified port and host
server.listen(port, hostname, () => {
  console.log(`🚀 Server is running at http://${hostname}:${port}/`);
});